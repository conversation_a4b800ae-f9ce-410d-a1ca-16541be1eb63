import Foundation
import AVFoundation
import Cocoa

/// Менеджер для управления воспроизведением звуков
class SoundManager {
    
    // MARK: - Properties
    
    private var audioPlayer: AVAudioPlayer?
    private let soundSettings = SoundSettings.shared
    
    // MARK: - Sound Types
    
    enum SoundType {
        case sessionCompleted
        case breakCompleted
    }
    
    // MARK: - Available Sounds
    
    struct SoundFile {
        let fileName: String
        let displayName: String
        let filePath: String
        
        init(fileName: String, displayName: String) {
            self.fileName = fileName
            self.displayName = displayName
            self.filePath = "Sounds/\(fileName)"
        }
    }
    
    // Список всех доступных звуков
    static let availableSounds: [SoundFile] = [
        SoundFile(fileName: "session_done/session_1.mp3", displayName: "Мелодия 1"),
        SoundFile(fileName: "break_done/break_1.mp3", displayName: "Мелодия 2"),
        SoundFile(fileName: "1.mp3", displayName: "Мелодия 3"),
        SoundFile(fileName: "2.mp3", displayName: "Мелодия 4"),
        SoundFile(fileName: "3.mp3", displayName: "Мелодия 5")
    ]
    
    // MARK: - Initialization
    
    init() {
        setupAudioSession()
    }
    
    // MARK: - Public Methods
    
    /// Воспроизводит звук для указанного типа события
    func playSound(for type: SoundType) {
        switch type {
        case .sessionCompleted:
            NSLog("🔊 SoundManager: Событие завершения сессии. Включен: \(soundSettings.isSessionSoundEnabled), файл: \(soundSettings.selectedSessionSound)")
            if soundSettings.isSessionSoundEnabled {
                NSLog("🔊 SoundManager: Воспроизводим звук завершения сессии")
                playSound(fileName: soundSettings.selectedSessionSound)
            } else {
                NSLog("🔇 SoundManager: Звук завершения сессии отключен")
            }
        case .breakCompleted:
            NSLog("🔊 SoundManager: Событие завершения отдыха. Включен: \(soundSettings.isBreakSoundEnabled), файл: \(soundSettings.selectedBreakSound)")
            if soundSettings.isBreakSoundEnabled {
                NSLog("🔊 SoundManager: Воспроизводим звук завершения отдыха")
                NSLog("🔊 SoundManager: Вызываем playSound с файлом: \(soundSettings.selectedBreakSound)")
                playSound(fileName: soundSettings.selectedBreakSound)
            } else {
                NSLog("🔇 SoundManager: Звук завершения отдыха отключен")
            }
        }
    }
    
    /// Воспроизводит конкретный звуковой файл (для тестирования)
    func playSound(fileName: String) {
        NSLog("🔊 SoundManager: Попытка воспроизвести файл: \(fileName)")

        guard let soundFile = Self.availableSounds.first(where: { $0.fileName == fileName }) else {
            NSLog("❌ SoundManager: Звуковой файл не найден в списке: \(fileName)")
            return
        }

        // Пытаемся найти файл в разных местах
        var url: URL?

        NSLog("🔍 SoundManager: Bundle.main.bundlePath: \(Bundle.main.bundlePath)")
        NSLog("🔍 SoundManager: Bundle.main.resourcePath: \(Bundle.main.resourcePath ?? "nil")")

        // Сначала пробуем найти в Bundle
        if soundFile.fileName.contains("/") {
            let components = soundFile.fileName.split(separator: "/")
            if components.count == 2 {
                let subdirectory = "Sounds/\(components[0])"
                let filename = String(components[1]).replacingOccurrences(of: ".mp3", with: "")
                NSLog("🔍 SoundManager: Ищем в Bundle: \(filename).mp3 в \(subdirectory)")
                url = Bundle.main.url(forResource: filename, withExtension: "mp3", subdirectory: subdirectory)
                NSLog("🔍 SoundManager: Bundle.main.url результат: \(url?.path ?? "nil")")
            }
        } else {
            let filename = soundFile.fileName.replacingOccurrences(of: ".mp3", with: "")
            NSLog("🔍 SoundManager: Ищем в Bundle: \(filename).mp3 в Sounds")
            url = Bundle.main.url(forResource: filename, withExtension: "mp3", subdirectory: "Sounds")
            NSLog("🔍 SoundManager: Bundle.main.url результат: \(url?.path ?? "nil")")
        }

        // Если не нашли в Bundle, пробуем найти в папке Sounds рядом с приложением
        if url == nil {
            let bundlePath = Bundle.main.bundlePath
            let soundsPath = URL(fileURLWithPath: bundlePath).deletingLastPathComponent().appendingPathComponent("Sounds").appendingPathComponent(soundFile.fileName)
            NSLog("🔍 SoundManager: Ищем рядом с приложением: \(soundsPath.path)")
            if FileManager.default.fileExists(atPath: soundsPath.path) {
                url = soundsPath
                NSLog("🔍 SoundManager: Найден рядом с приложением!")
            } else {
                NSLog("🔍 SoundManager: Не найден рядом с приложением")
            }
        }

        // Пробуем прямой путь в Resources
        if url == nil {
            let resourcesPath = Bundle.main.bundlePath + "/Contents/Resources/Sounds/" + soundFile.fileName
            NSLog("🔍 SoundManager: Пробуем прямой путь: \(resourcesPath)")
            if FileManager.default.fileExists(atPath: resourcesPath) {
                url = URL(fileURLWithPath: resourcesPath)
                NSLog("🔍 SoundManager: Найден по прямому пути!")
            } else {
                NSLog("🔍 SoundManager: Не найден по прямому пути")
            }
        }

        guard let finalUrl = url else {
            NSLog("❌ SoundManager: Не удалось найти файл: \(soundFile.fileName)")
            return
        }

        NSLog("🔊 SoundManager: Найден файл: \(finalUrl.path)")

        do {
            audioPlayer = try AVAudioPlayer(contentsOf: finalUrl)
            audioPlayer?.volume = soundSettings.soundVolume // Используем настройку громкости
            audioPlayer?.play()
            NSLog("🔊 SoundManager: Воспроизводится звук: \(soundFile.displayName) с громкостью \(Int(soundSettings.soundVolume * 100))%")
        } catch {
            NSLog("❌ SoundManager: Ошибка воспроизведения звука: \(error.localizedDescription)")
        }
    }
    
    /// Останавливает воспроизведение звука
    func stopSound() {
        audioPlayer?.stop()
        audioPlayer = nil
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        // На macOS AVAudioSession недоступен, поэтому просто логируем
        print("🔊 SoundManager: Аудио сессия готова (macOS)")
    }
    
    // MARK: - Static Helper Methods
    
    /// Возвращает звук по умолчанию для сессий
    static func getDefaultSessionSound() -> String {
        return "session_done/session_1.mp3"
    }
    
    /// Возвращает звук по умолчанию для отдыхов
    static func getDefaultBreakSound() -> String {
        return "break_done/break_1.mp3"
    }
    
    /// Возвращает отображаемое имя для звукового файла
    static func getDisplayName(for fileName: String) -> String {
        return availableSounds.first(where: { $0.fileName == fileName })?.displayName ?? fileName
    }
    
    /// Проверяет, существует ли звуковой файл
    static func soundExists(fileName: String) -> Bool {
        return availableSounds.contains(where: { $0.fileName == fileName })
    }
}

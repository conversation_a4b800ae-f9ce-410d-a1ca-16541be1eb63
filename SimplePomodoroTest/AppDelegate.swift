import Cocoa

func writeDebugLog(_ message: String) {
    let logMessage = "\(Date()): \(message)\n"
    let logPath = "/tmp/uProd_debug.log"
    if let data = logMessage.data(using: .utf8) {
        if FileManager.default.fileExists(atPath: logPath) {
            if let fileHandle = FileHandle(forWritingAtPath: logPath) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
                fileHandle.closeFile()
            }
        } else {
            try? data.write(to: URL(fileURLWithPath: logPath))
        }
    }
}

// MARK: - Константы приложения

/// Версия приложения для отображения в меню
/// Если не удается получить версию из Bundle, используется эта константа
private let APP_VERSION_FALLBACK = "0.2.4"

class AppDelegate: NSObject, NSApplicationDelegate {

    var statusItem: NSStatusItem!
    var pomodoroTimer: PomodoroTimer!
    var notificationWindow: IntervalNotificationWindow?
    var modernCompletionWindow: ModernCompletionWindow? // Новое современное окно
    var settingsWindow: SettingsWindow?
    var statisticsManager: StatisticsManager!
    var statisticsWindow: StatisticsWindow?
    var workPatternAnalyzer: WorkPatternAnalyzer!
    var demoDataManager: DemoDataManager!
    var projectManager: ProjectManager!
    var projectsWindow: ProjectsWindow?
    var quickProjectSelectionWindow: QuickProjectSelectionWindow?
    var soundManager: SoundManager!
    var computerTimeTracker: ComputerTimeTracker!

    // Окна отдыха
    var breakStartWindow: BreakStartWindow?
    var breakEndWindow: BreakEndWindow?
    var breakTypeWindow: BreakTypeSelectionWindow?

    // Таймер для отложенного напоминания "Later" (10 минут)
    private var postponeTimer: Timer?

    // Отложенный отдых - зеленый счетчик
    private var isBreakPostponed = false
    private var postponedBreakStartTime: Date?
    private var originalBreakDuration: TimeInterval = 0  // Сколько длился оригинальный отдых
    private var postponeUpdateTimer: Timer?

    var motivationManager: MotivationManager!

    // Текущий выбранный проект для запуска интервала
    var currentProjectId: UUID?

    // Флаги для отслеживания поведения во время отдыха
    private var userPromisedToRest = false // Пользователь нажал "Иду отдыхать"
    private var userRestingAtComputer = false // Пользователь нажал "Я отдыхаю за компом"

    // Последний использованный проект (для показа в окне завершения отдыха)
    var lastUsedProjectId: UUID?

    // Качество последнего отдыха
    var lastBreakQuality: Int = 100

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        NSLog("🚀 uProd: App starting...")

        // Скрываем из Dock
        NSApp.setActivationPolicy(.accessory)
        NSLog("🚀 uProd: Set activation policy to accessory")

        // Инициализируем автозапуск
        LaunchAtLoginManager.shared.initializeOnFirstLaunch()
        NSLog("🚀 uProd: Launch at login initialized")

        // Инициализируем статистику
        setupStatistics()
        NSLog("🚀 uProd: Statistics setup complete")

        // Инициализируем таймер
        setupPomodoroTimer()
        NSLog("🚀 uProd: Timer setup complete")

        // Создаём menu bar item
        setupStatusItem()
        NSLog("🚀 uProd: Status item setup complete")

        NSLog("🚀 uProd: App fully initialized")

        // Тестируем звуковую систему
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            SoundTest.runTest()
        }

        // Тесты кнопок отключены - используем новый кастомный интерфейс
        // DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
        //     ButtonTestManager.shared.runButtonTests()
        // }


    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // Не завершаем приложение при закрытии последнего окна
        return false
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    private func setupStatistics() {
        statisticsManager = StatisticsManager()
        projectManager = ProjectManager()
        workPatternAnalyzer = WorkPatternAnalyzer(statisticsManager: statisticsManager)
        motivationManager = MotivationManager(statisticsManager: statisticsManager, analyzer: workPatternAnalyzer)
        demoDataManager = DemoDataManager(statisticsManager: statisticsManager)
        soundManager = SoundManager()

        // Инициализируем и запускаем трекер времени за компьютером
        computerTimeTracker = ComputerTimeTracker(statisticsManager: statisticsManager)
        computerTimeTracker.startTracking()

        // Проверяем и исправляем настройки звуков
        SoundSettings.shared.validateAndFixSettings()
    }

    private func setupPomodoroTimer() {
        pomodoroTimer = PomodoroTimer()

        // Связываем SoundManager с таймерами
        pomodoroTimer.soundManager = soundManager
        pomodoroTimer.breakTimer.soundManager = soundManager

        // Настраиваем колбэки
        pomodoroTimer.onStateChanged = { [weak self] state in
            DispatchQueue.main.async {
                self?.updateStatusItem()
                self?.updateMenu()
            }
        }

        pomodoroTimer.onTimeUpdate = { [weak self] timeRemaining, overtimeElapsed in
            DispatchQueue.main.async {
                self?.updateStatusItem()
            }
        }

        pomodoroTimer.onIntervalCompleted = { [weak self] in
            DispatchQueue.main.async {
                self?.showCompletionWindow()
            }
        }

        // ОТКЛЮЧЕНО: Старая логика напоминаний заменена на onOvertimeColorChanged
        // pomodoroTimer.onReminderTriggered = { [weak self] reminderCount in
        //     DispatchQueue.main.async {
        //         self?.showReminderWindow(reminderCount: reminderCount)
        //     }
        // }

        pomodoroTimer.onOvertimeColorChanged = { [weak self] colorLevel in
            DispatchQueue.main.async {
                self?.handleOvertimeColorChange(colorLevel: colorLevel)
            }
        }

        pomodoroTimer.onFullIntervalCompleted = { [weak self] duration in
            DispatchQueue.main.async {
                self?.statisticsManager.recordCompletedInterval(duration: duration, projectId: self?.currentProjectId)
                self?.motivationManager.checkAndSendMotivationalNotification()
                // Сохраняем последний использованный проект для показа в окне завершения отдыха
                self?.lastUsedProjectId = self?.currentProjectId
                // Сбрасываем текущий проект после завершения интервала
                self?.currentProjectId = nil
            }
        }

        pomodoroTimer.onBreakStarted = { [weak self] in
            DispatchQueue.main.async {
                // НЕ показываем окно сразу - только запускаем отдых
                print("🍃 AppDelegate: Отдых начат, мониторинг активности запущен")
            }
        }

        pomodoroTimer.onBreakCompleted = { [weak self] breakStats in
            DispatchQueue.main.async {
                // Сбрасываем флаги отдыха
                self?.userPromisedToRest = false
                self?.userRestingAtComputer = false

                // СРАЗУ запускаем зеленый счетчик для бесшовного перехода
                self?.startPostponedBreakCounter(with: breakStats)

                // Затем показываем окно
                self?.showBreakEndWindow()
            }
        }

        pomodoroTimer.onBreakActivityDetected = { [weak self] in
            DispatchQueue.main.async {
                self?.showBreakActivityWarning()
            }
        }

        // Обработка качества отдыха из BreakTimer
        pomodoroTimer.breakTimer.onBreakQualityCalculated = { [weak self] qualityPercentage in
            DispatchQueue.main.async {
                self?.lastBreakQuality = qualityPercentage
                print("📊 AppDelegate: Получено качество отдыха: \(qualityPercentage)%")
            }
        }

        // Обработка полноценных отдыхов (записываются в статистику)
        pomodoroTimer.onFullBreakCompleted = { [weak self] stats in
            DispatchQueue.main.async {
                self?.statisticsManager.recordCompletedBreak(
                    duration: stats.duration,
                    wasComputerActive: stats.wasComputerActive,
                    computerActiveTime: stats.computerActiveTime,
                    qualityPercentage: self?.lastBreakQuality ?? 100
                )
            }
        }

        // Обработка тестовых отдыхов (НЕ записываются в статистику)
        pomodoroTimer.onBreakStatisticsReady = { [weak self] stats in
            // Этот колбэк теперь используется только для тестовых отдыхов
            // Ничего не записываем в статистику
            print("🧪 AppDelegate: Тестовый отдых завершен, не записываем в статистику")
        }
    }

    private func setupStatusItem() {
        NSLog("🚀 uProd: Creating status item...")
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem.button {
            button.title = pomodoroTimer.getStatusText()
            button.toolTip = "uProd - Productivity Timer"
            button.action = #selector(statusItemClicked)
            button.target = self
            NSLog("🚀 uProd: Status item button configured with title: \(button.title)")
        } else {
            NSLog("❌ uProd: Failed to get status item button")
        }

        // Создаем меню
        writeDebugLog("🔧 AppDelegate: setupStatusItem вызывает updateMenu")
        updateMenu()
        writeDebugLog("🔧 AppDelegate: setupStatusItem updateMenu завершен")

        // Подписываемся на уведомления об изменении избранных
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(favoritesChanged),
            name: NSNotification.Name("FavoritesChanged"),
            object: nil
        )

        // Подписываемся на уведомления об изменении порядка проектов
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(projectOrderChanged),
            name: NSNotification.Name("ProjectOrderChanged"),
            object: nil
        )

        NSLog("🚀 uProd: Status item setup complete")
    }

    @objc private func statusItemClicked() {
        // При клике показываем меню
        // Меню автоматически показывается при клике, если оно установлено
    }

    private func updateStatusItem() {
        // Получаем название проекта если есть
        var projectName: String? = nil
        if let projectId = currentProjectId,
           let project = projectManager.getProject(by: projectId) {
            projectName = project.effectiveEmoji
            print("🎯 AppDelegate: updateStatusItem - проект найден: \(project.name) (\(project.effectiveEmoji))")
        } else {
            print("🎯 AppDelegate: updateStatusItem - currentProjectId=\(currentProjectId?.uuidString ?? "nil")")
        }

        // Если отдых отложен, показываем зеленый счетчик
        let statusText: String
        if isBreakPostponed {
            // Во время отложенного отдыха НЕ показываем иконку проекта - только листочек и время
            statusText = getPostponedBreakStatusText(projectName: nil)
        } else {
            // Передаем projectName только во время работы и переработки, не во время отдыха
            let projectForStatus = (pomodoroTimer.state == .working || pomodoroTimer.state == .overtime) ? projectName : nil
            statusText = pomodoroTimer.getStatusText(projectName: projectForStatus)
        }

        // Проверяем, нужен ли красный фон (с 10 минут переработки)
        let needsRedBackground = pomodoroTimer.state == .overtime && pomodoroTimer.overtimeElapsed >= 600 // 10 минут = 600 секунд

        if needsRedBackground {
            // Создаем изображение с красным фоном и белым текстом
            let image = createRedBackgroundImage(with: statusText)
            statusItem.button?.image = image
            statusItem.button?.title = ""
            statusItem.button?.attributedTitle = NSAttributedString(string: "")
        } else {
            // Обычный цветной текст без фона
            statusItem.button?.image = nil

            let textColor: NSColor
            if isBreakPostponed {
                textColor = NSColor(red: 0.5, green: 0.8, blue: 0.5, alpha: 1.0) // Тот же зеленый как у обычного отдыха
            } else if pomodoroTimer.state == .overtime {
                textColor = getOvertimeTextColor()
            } else if pomodoroTimer.state == .onBreak {
                textColor = NSColor(red: 0.5, green: 0.8, blue: 0.5, alpha: 1.0) // Более насыщенный зеленый для отдыха
            } else {
                textColor = NSColor.controlTextColor
            }

            // Полностью очищаем button
            statusItem.button?.title = ""
            statusItem.button?.attributedTitle = NSAttributedString(string: "")

            // Создаем attributed string с цветом для каждого символа
            let attributedString = NSMutableAttributedString()
            let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)

            // Добавляем каждый символ отдельно с принудительным цветом
            for char in statusText {
                let charString = String(char)
                let charAttributed = NSAttributedString(string: charString, attributes: [
                    .foregroundColor: textColor,
                    .font: font
                ])
                attributedString.append(charAttributed)
            }

            // Устанавливаем с принудительным обновлением
            statusItem.button?.attributedTitle = attributedString

            // Принудительно перерисовываем весь status item
            if let button = statusItem.button {
                button.setNeedsDisplay(button.bounds)
            }
        }
    }

    private func getOvertimeTextColor() -> NSColor {
        let minutes = Int(pomodoroTimer.overtimeElapsed) / 60
        return OvertimeConfig.getColor(for: minutes)
    }

    private func createRedBackgroundImage(with text: String) -> NSImage {
        let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: NSColor.white
        ]

        // Измеряем размер текста
        let textSize = text.size(withAttributes: textAttributes)

        // Делаем фон на всю высоту status bar (обычно 22px) с отступами слева-справа
        let statusBarHeight: CGFloat = 22
        let horizontalPadding: CGFloat = 6
        let imageSize = NSSize(width: textSize.width + horizontalPadding * 2, height: statusBarHeight)

        // Создаем изображение
        let image = NSImage(size: imageSize)
        image.lockFocus()

        // Рисуем красный фон на всю высоту
        let backgroundRect = NSRect(origin: .zero, size: imageSize)
        NSColor.systemRed.setFill()
        backgroundRect.fill()

        // Рисуем белый текст по центру
        let textRect = NSRect(
            x: horizontalPadding,
            y: (imageSize.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: textAttributes)

        image.unlockFocus()

        // НЕ устанавливаем template - чтобы сохранить красный цвет
        image.isTemplate = false

        return image
    }

    private func updateMenu() {
        writeDebugLog("🔧 AppDelegate: updateMenu вызван")
        let menu = NSMenu()

        if pomodoroTimer.isActive() {
            // Показываем информацию о текущем состоянии
            let statusItem = NSMenuItem(title: getStatusMenuText(), action: nil, keyEquivalent: "")
            statusItem.isEnabled = false
            menu.addItem(statusItem)

            menu.addItem(NSMenuItem.separator())

            // Кнопка завершения интервала
            let stopItem = NSMenuItem(title: "Завершить интервал", action: #selector(stopInterval), keyEquivalent: "")
            stopItem.target = self
            menu.addItem(stopItem)
        } else {
            // Избранные проекты для быстрого запуска
            let favoriteProjects = projectManager.getFavoriteProjects()
            let minutes = Int(PomodoroTimer.workDuration / 60)

            if favoriteProjects.isEmpty {
                // Если нет избранных проектов, показываем обычную кнопку
                let startItem = NSMenuItem(title: "Начать интервал (\(minutes) мин)", action: #selector(startInterval), keyEquivalent: "")
                startItem.target = self
                menu.addItem(startItem)
            } else {
                // Показываем кнопки для каждого избранного проекта
                for (index, project) in favoriteProjects.enumerated() {
                    let colorIndicator = createColorIndicator(for: project)
                    let title = "\(colorIndicator) Начать: \(project.name) (\(minutes) мин)"
                    let selector = #selector(startIntervalWithProject(_:))
                    let keyEquivalent = index < 3 ? "\(index + 1)" : ""

                    let projectItem = NSMenuItem(title: title, action: selector, keyEquivalent: keyEquivalent)
                    projectItem.target = self
                    projectItem.representedObject = project.id
                    menu.addItem(projectItem)
                }
            }

            menu.addItem(NSMenuItem.separator())
        }

        menu.addItem(NSMenuItem.separator())

        // Кнопка управления проектами
        let projectsItem = NSMenuItem(title: "📁 Проекты...", action: #selector(showProjectManagement), keyEquivalent: "p")
        projectsItem.target = self
        menu.addItem(projectsItem)
        writeDebugLog("🔧 AppDelegate: добавлен пункт меню 'Проекты...', target: \(String(describing: projectsItem.target)), action: \(String(describing: projectsItem.action))")

        // Кнопка настроек
        let settingsItem = NSMenuItem(title: "⚙️ Настройки...", action: #selector(showSettings), keyEquivalent: ",")
        settingsItem.target = self
        menu.addItem(settingsItem)

        // Кнопка статистики
        let statisticsItem = NSMenuItem(title: "📊 Статистика...", action: #selector(showStatistics), keyEquivalent: "s")
        statisticsItem.target = self
        menu.addItem(statisticsItem)

        menu.addItem(NSMenuItem.separator())

        // Кнопка тестового запуска
        let testItem = NSMenuItem(title: "🧪 Тестовый запуск (3 сек)", action: #selector(startTestInterval), keyEquivalent: "t")
        testItem.target = self
        menu.addItem(testItem)

        // Кнопка тестового отдыха
        let testBreakItem = NSMenuItem(title: "🧪 Тестовый отдых (3 сек)", action: #selector(startTestBreak), keyEquivalent: "b")
        testBreakItem.target = self
        menu.addItem(testBreakItem)

        menu.addItem(NSMenuItem.separator())

        // Версия приложения
        let versionItem = NSMenuItem(title: "Версия \(getAppVersion())", action: nil, keyEquivalent: "")
        versionItem.isEnabled = false
        menu.addItem(versionItem)

        menu.addItem(NSMenuItem.separator())

        // Кнопка выхода
        let quitItem = NSMenuItem(title: "🚪 Выход", action: #selector(quitApp), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)

        statusItem.menu = menu
    }

    private func getStatusMenuText() -> String {
        switch pomodoroTimer.state {
        case .idle:
            return "Готов к работе"
        case .working:
            let timeText = "Работаем: \(pomodoroTimer.formatTime(pomodoroTimer.timeRemaining))"
            if let projectId = currentProjectId,
               let project = projectManager.getProject(by: projectId) {
                return "\(project.effectiveEmoji) \(project.name) - \(timeText)"
            }
            return timeText
        case .overtime:
            let timeText = "Переработка: +\(pomodoroTimer.formatTime(pomodoroTimer.overtimeElapsed))"
            if let projectId = currentProjectId,
               let project = projectManager.getProject(by: projectId) {
                return "\(project.effectiveEmoji) \(project.name) - \(timeText)"
            }
            return timeText
        case .onBreak:
            return "🍃 Отдых: \(pomodoroTimer.formatTime(pomodoroTimer.breakTimeRemaining))"
        }
    }

    private func getAppVersion() -> String {
        // Сначала пытаемся прочитать версию из todo.md
        if let version = readVersionFromTodoMd() {
            return version
        }

        // Если не получилось, берем из Bundle
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            return version
        }

        // Последний fallback
        return APP_VERSION_FALLBACK
    }

    private func readVersionFromTodoMd() -> String? {
        // Ищем файл todo.md в корне проекта
        let currentDirectory = FileManager.default.currentDirectoryPath
        let todoPath = "\(currentDirectory)/todo.md"

        // Если файл не найден в текущей директории, пробуем относительный путь
        let possiblePaths = [
            todoPath,
            "\(currentDirectory)/../todo.md",
            "\(currentDirectory)/../../todo.md"
        ]

        for path in possiblePaths {
            if let content = try? String(contentsOfFile: path, encoding: .utf8) {
                // Ищем строку с APP_VERSION =
                let lines = content.components(separatedBy: .newlines)
                for line in lines {
                    if line.contains("APP_VERSION") && line.contains("=") {
                        // Извлекаем версию из строки типа "**APP_VERSION = 0.2.4**"
                        let components = line.components(separatedBy: "=")
                        if components.count >= 2 {
                            let versionPart = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                            // Убираем возможные символы markdown (**) и пробелы
                            let cleanVersion = versionPart.replacingOccurrences(of: "*", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                            if !cleanVersion.isEmpty {
                                print("📋 AppDelegate: Версия из todo.md: \(cleanVersion)")
                                return cleanVersion
                            }
                        }
                    }
                }
            }
        }

        print("📋 AppDelegate: Не удалось прочитать версию из todo.md")
        return nil
    }

    @objc private func startInterval() {
        currentProjectId = nil
        pomodoroTimer.startInterval()
    }

    @objc private func startIntervalWithProject(_ sender: NSMenuItem) {
        if let projectId = sender.representedObject as? UUID {
            currentProjectId = projectId
            projectManager.markProjectAsUsed(projectId)
            pomodoroTimer.startInterval()
        }
    }

    @objc private func startTestInterval() {
        // Запускаем тестовый интервал на 3 секунды
        currentProjectId = nil
        pomodoroTimer.startInterval(testDuration: 3)
    }

    @objc private func startTestBreak() {
        // Запускаем тестовый отдых на 3 секунды
        pomodoroTimer.startBreakWithType(isLong: false, testDuration: 3)
    }



    @objc private func stopInterval() {
        pomodoroTimer.stopInterval()
    }

    @objc private func showProjectManagement() {
        NSLog("🔧 AppDelegate: showProjectManagement вызван")
        if projectsWindow == nil {
            NSLog("🔧 AppDelegate: создаем новый ProjectsWindow")
            projectsWindow = ProjectsWindow(projectManager: projectManager)
        }
        NSLog("🔧 AppDelegate: вызываем showWindow")
        projectsWindow?.showWindow()
        NSLog("🔧 AppDelegate: showProjectManagement завершен")
    }

    // MARK: - Project Menu Creation

    private func createOtherProjectsMenu() -> NSMenu {
        let menu = NSMenu()
        let activeProjects = projectManager.getActiveProjects()
        let favoriteProjects = projectManager.getFavoriteProjects()
        let favoriteIds = Set(favoriteProjects.map { $0.id })

        // Группируем проекты по типам
        let projectsByType = Dictionary(grouping: activeProjects) { $0.type }
        let minutes = Int(PomodoroTimer.workDuration / 60)

        for projectType in ProjectType.allCases {
            if let projects = projectsByType[projectType], !projects.isEmpty {
                // Добавляем заголовок типа
                let typeHeader = NSMenuItem(title: projectType.displayName, action: nil, keyEquivalent: "")
                typeHeader.isEnabled = false
                menu.addItem(typeHeader)

                // Добавляем проекты этого типа
                for project in projects.sorted(by: { $0.name < $1.name }) {
                    // Пропускаем проекты, которые уже в избранном
                    if favoriteIds.contains(project.id) { continue }

                    let colorIndicator = createColorIndicator(for: project)
                    let title = "  \(colorIndicator) \(project.name) (\(minutes) мин)"
                    let projectItem = NSMenuItem(title: title, action: #selector(startIntervalWithProject(_:)), keyEquivalent: "")
                    projectItem.target = self
                    projectItem.representedObject = project.id
                    menu.addItem(projectItem)
                }

                menu.addItem(NSMenuItem.separator())
            }
        }

        // Удаляем последний разделитель если он есть
        if menu.items.last?.isSeparatorItem == true {
            menu.removeItem(menu.items.last!)
        }

        return menu
    }

    // MARK: - Color Indicator Helper

    private func createColorIndicator(for project: Project) -> String {
        // Приоритет: сначала иконка проекта, потом иконка категории
        if let customEmoji = project.customEmoji, !customEmoji.isEmpty {
            return customEmoji
        } else {
            return project.type.emoji
        }
    }

    @objc private func showSettings() {
        if settingsWindow == nil {
            settingsWindow = SettingsWindow()
            settingsWindow?.setOnSettingsChanged { [weak self] newDuration in
                self?.pomodoroTimer.updateWorkDuration(newDuration)
                self?.updateStatusItem()
                self?.updateMenu()
            }
        }
        settingsWindow?.updateIntervalDuration(PomodoroTimer.workDuration)
        settingsWindow?.showWindow()
    }

    @objc private func showStatistics() {
        if statisticsWindow == nil {
            statisticsWindow = StatisticsWindow(statisticsManager: statisticsManager, projectManager: projectManager, motivationManager: motivationManager)
        }
        statisticsWindow?.showWindow()
    }

    @objc private func showCompletionWindowDemo() {
        NSLog("🎨 AppDelegate: Демонстрация нового окна завершения")
        let completionWindow = ModernCompletionWindow()
        completionWindow.showWithAnimation()
    }

    @objc private func createDemoData() {
        demoDataManager.createDemoData()
        updateStatusItem()
        updateMenu()

        // Если окно статистики открыто, обновляем его
        statisticsWindow?.refreshStatistics()

        // Показываем уведомление
        showAlert(title: "Демо данные созданы", message: "Созданы демо данные с различными проблемами по неделям. Откройте статистику для просмотра.")
    }

    @objc private func clearAllData() {
        // Показываем подтверждение
        let alert = NSAlert()
        alert.messageText = "Очистить все данные?"
        alert.informativeText = "Это действие удалит все записанные интервалы. Отменить это действие будет невозможно."
        alert.addButton(withTitle: "Очистить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .warning

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            statisticsManager.clearAllIntervals()
            updateStatusItem()
            updateMenu()

            // Если окно статистики открыто, обновляем его
            statisticsWindow?.refreshStatistics()

            showAlert(title: "Данные очищены", message: "Все интервалы были удалены.")
        }
    }



    @objc private func showDemoDescription() {
        let description = demoDataManager.getDemoDataDescription()
        showAlert(title: "Описание демо данных", message: description)
    }

    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }

    @objc private func quitApp() {
        NSApplication.shared.terminate(nil)
    }

    @objc private func favoritesChanged() {
        // Обновляем меню при изменении избранных проектов
        DispatchQueue.main.async { [weak self] in
            self?.updateMenu()
        }
    }

    @objc private func projectOrderChanged() {
        // Обновляем меню при изменении порядка проектов
        DispatchQueue.main.async { [weak self] in
            self?.updateMenu()
        }
    }

    // MARK: - Notification Window Methods

    private func showCompletionWindow() {
        print("🎨 AppDelegate: Показ НОВОГО современного окна завершения")

        // Воспроизводим звук завершения сессии сразу при показе окна
        if pomodoroTimer.naturallyCompleted {
            NSLog("🔊 AppDelegate: Воспроизводим звук завершения сессии при показе окна")
            soundManager.playSound(for: .sessionCompleted)
        }

        // Закрываем предыдущие окна если они есть
        if let existingWindow = notificationWindow {
            existingWindow.orderOut(nil)
            notificationWindow = nil
        }
        if let existingModernWindow = modernCompletionWindow {
            existingModernWindow.orderOut(nil)
            modernCompletionWindow = nil
        }

        // Создаем новое современное окно
        let modernWindow = ModernCompletionWindow(
            contentRect: NSRect(x: 0, y: 0, width: 340, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // Позиционируем окно возле status item
        if let button = statusItem.button, let window = button.window {
            let buttonFrame = button.convert(button.bounds, to: nil)
            let screenFrame = window.convertToScreen(buttonFrame)
            modernWindow.positionRelativeToStatusItem(statusItemFrame: screenFrame)
        }

        // Настраиваем колбэки для обычного окна завершения
        modernWindow.onComplete = { [weak self] in
            print("🎨 AppDelegate: Колбэк завершения интервала")
            self?.modernCompletionWindow = nil // Обнуляем ссылку при закрытии

            // Проверяем, нужно ли показать выбор типа отдыха (прогрессивная логика)
            if self?.pomodoroTimer.shouldShowBreakTypeSelection() == true {
                print("🎨 AppDelegate: Показываем окно выбора типа отдыха")
                // НЕ вызываем completeInterval() - таймер переработки должен продолжать идти
                self?.showBreakTypeSelectionWindow()
            } else {
                print("🎨 AppDelegate: Начинаем обычный короткий отдых")
                // Только здесь вызываем completeInterval() и останавливаем таймер переработки
                self?.pomodoroTimer.completeInterval()
                self?.pomodoroTimer.startBreakWithType(isLong: false)
            }
        }

        modernWindow.onExtend1 = { [weak self] in
            print("🎨 AppDelegate: Колбэк продления на 1 минуту")
            self?.modernCompletionWindow = nil // Обнуляем ссылку при закрытии
            self?.pomodoroTimer.extendInterval(minutes: 1)
        }

        modernWindow.onExtend5 = { [weak self] in
            print("🎨 AppDelegate: Колбэк продления на 5 минут")
            self?.modernCompletionWindow = nil // Обнуляем ссылку при закрытии
            self?.pomodoroTimer.extendInterval(minutes: 5)
        }

        // Сохраняем ссылку на окно
        modernCompletionWindow = modernWindow
        modernWindow.showWithAnimation()
    }

    private func showBreakTypeSelectionWindow() {
        print("🎨 AppDelegate: Показ окна выбора типа отдыха")

        // НЕ останавливаем таймер переработки - время выбора должно засчитываться как переработка

        // Создаем новое окно выбора типа отдыха (точно такой же размер как ModernCompletionWindow)
        breakTypeWindow = BreakTypeSelectionWindow(
            contentRect: NSRect(x: 0, y: 0, width: 380, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // Позиционируем окно возле status item
        if let button = statusItem.button, let window = button.window {
            let buttonFrame = button.convert(button.bounds, to: nil)
            let screenFrame = window.convertToScreen(buttonFrame)
            breakTypeWindow?.positionRelativeToStatusItem(statusItemFrame: screenFrame)
        }

        // Настраиваем окно с количеством интервалов
        breakTypeWindow?.configureForIntervals(pomodoroTimer.completedIntervals)

        // Настраиваем колбэки
        breakTypeWindow?.onShortBreak = { [weak self] in
            print("🎨 AppDelegate: Выбран короткий отдых")
            // Теперь завершаем интервал (останавливаем таймер переработки) и начинаем отдых
            self?.pomodoroTimer.completeInterval()
            self?.pomodoroTimer.startBreakWithType(isLong: false)
            self?.breakTypeWindow = nil
        }

        breakTypeWindow?.onLongBreak = { [weak self] in
            print("🎨 AppDelegate: Выбран длинный отдых")
            // Уведомляем таймер о выборе Long отдыха (сбрасывает счетчик)
            self?.pomodoroTimer.userSelectedLongBreak()
            // Теперь завершаем интервал (останавливаем таймер переработки) и начинаем отдых
            self?.pomodoroTimer.completeInterval()
            self?.pomodoroTimer.startBreakWithType(isLong: true)
            self?.breakTypeWindow = nil
        }

        breakTypeWindow?.showWithAnimation()
    }

    private func handleOvertimeColorChange(colorLevel: Int) {
        print("🎨 AppDelegate: Изменение цвета переработки на уровень \(colorLevel)")

        // Если современное окно уже открыто, обновляем его
        if let window = modernCompletionWindow {
            print("🎨 AppDelegate: Обновление существующего современного окна")
            window.updateMessage(reminderCount: colorLevel)
        } else if colorLevel > 0 {
            // Создаем окно только если уровень больше 0 (не обычный цвет)
            print("🎨 AppDelegate: Создание нового современного окна")
            showCompletionWindow()
            modernCompletionWindow?.updateMessage(reminderCount: colorLevel)
        }
    }

    private func showReminderWindow(reminderCount: Int) {
        print("🎨 AppDelegate: Показ напоминания #\(reminderCount) (старая логика - только для совместимости)")

        // Оставляем старую логику для совместимости, но теперь основная логика в handleOvertimeColorChange
        if let window = notificationWindow {
            window.updateMessage(reminderCount: reminderCount)
            window.makeKeyAndOrderFront(nil)
        }
    }

    // MARK: - Break Windows Management

    private func showBreakStartWindow() {
        print("🌿 AppDelegate: Показ окна начала отдыха")

        breakStartWindow = BreakStartWindow()

        // Позиционируем окно относительно status item
        if let statusButton = statusItem.button {
            let statusFrame = statusButton.window?.convertToScreen(statusButton.frame) ?? NSRect.zero
            breakStartWindow?.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Настраиваем колбэки
        breakStartWindow?.onStartBreak = { [weak self] in
            print("🌿 AppDelegate: Пользователь подтвердил начало отдыха - обещал отдыхать")
            self?.userPromisedToRest = true
            self?.userRestingAtComputer = false
            self?.breakStartWindow = nil
        }

        breakStartWindow?.onSkipBreak = { [weak self] in
            print("🌿 AppDelegate: Пользователь пропустил отдых")
            self?.pomodoroTimer.stopBreak()
            self?.breakStartWindow = nil
        }

        breakStartWindow?.onHideForBreak = { [weak self] in
            print("🌿 AppDelegate: Пользователь отдыхает за компом - отключаем повторные уведомления")
            self?.userRestingAtComputer = true
            self?.userPromisedToRest = false
            self?.breakStartWindow = nil
            // Окно скрывается, отдых продолжается, но уведомления об активности отключены
        }

        // Обновляем таймер в окне
        breakStartWindow?.updateTimeRemaining(pomodoroTimer.breakTimeRemaining)

        // Настраиваем обновление времени в окне отдыха
        setupBreakWindowUpdates()

        // Обновляем статус бар
        updateStatusItem()

        breakStartWindow?.showWithAnimation()
    }

    private func showBreakEndWindow() {
        print("🌿 AppDelegate: Показ окна завершения отдыха")

        // Воспроизводим звук завершения отдыха сразу при показе окна
        NSLog("🔊 AppDelegate: Воспроизводим звук завершения отдыха при показе окна")
        NSLog("🔊 AppDelegate: SoundManager существует: \(soundManager != nil)")
        NSLog("🔊 AppDelegate: Настройки звука отдыха - включен: \(SoundSettings.shared.isBreakSoundEnabled), файл: \(SoundSettings.shared.selectedBreakSound)")
        soundManager.playSound(for: .breakCompleted)

        breakEndWindow = BreakEndWindow()

        // Позиционируем окно относительно status item
        if let statusButton = statusItem.button {
            let statusFrame = statusButton.window?.convertToScreen(statusButton.frame) ?? NSRect.zero
            breakEndWindow?.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Получаем статистику отдыха
        if let stats = pomodoroTimer.getBreakStatistics() {
            breakEndWindow?.updateStatistics(with: stats)
        }

        // Передаем информацию о последнем проекте
        breakEndWindow?.getLastUsedProject = { [weak self] in
            guard let self = self else { return nil }

            // Сначала пытаемся получить последний использованный проект
            if let projectId = self.lastUsedProjectId,
               let project = self.projectManager.getProject(by: projectId) {
                let emoji = project.customEmoji?.isEmpty == false ? project.customEmoji! : project.type.emoji
                return (name: project.name, emoji: emoji)
            }

            // Если последнего проекта нет, берем первый из избранных
            let favoriteProjects = self.projectManager.getFavoriteProjects()
            if let firstProject = favoriteProjects.first {
                let emoji = firstProject.customEmoji?.isEmpty == false ? firstProject.customEmoji! : firstProject.type.emoji
                return (name: firstProject.name, emoji: emoji)
            }

            return nil
        }

        // Отладочная информация о проектах
        print("🎯 AppDelegate: showBreakEndWindow - currentProjectId=\(currentProjectId?.uuidString ?? "nil")")
        print("🎯 AppDelegate: showBreakEndWindow - lastUsedProjectId=\(lastUsedProjectId?.uuidString ?? "nil")")

        // Получаем информацию о проектах
        let allProjects = projectManager.getAllProjects()
        print("🎯 AppDelegate: Всего проектов: \(allProjects.count)")
        if !allProjects.isEmpty {
            print("🎯 AppDelegate: Первый проект: \(allProjects[0].name) (\(allProjects[0].effectiveEmoji))")
        }

        // Устанавливаем currentProjectId равным отображаемому проекту, если он не установлен
        if currentProjectId == nil {
            if let lastProject = lastUsedProjectId {
                currentProjectId = lastProject
                print("🎯 AppDelegate: Установили currentProjectId = lastUsedProjectId (\(lastProject.uuidString))")
            } else if !allProjects.isEmpty {
                // Если нет последнего проекта, используем первый доступный
                currentProjectId = allProjects[0].id
                lastUsedProjectId = allProjects[0].id
                print("🎯 AppDelegate: Установили currentProjectId = первый проект (\(allProjects[0].name))")
            } else {
                print("🎯 AppDelegate: Нет доступных проектов")
            }
        }

        // Обновляем кнопку проекта после установки колбэка
        breakEndWindow?.updateProjectButton()

        // Настраиваем колбэки
        breakEndWindow?.onStartInterval = { [weak self] in
            print("🌿 AppDelegate: Начать новый интервал с выбранным проектом")

            // Останавливаем отложенный отдых, если он был
            self?.stopPostponedBreak()

            // Получаем проект, который отображается в окне (тот же, что видит пользователь)
            var projectToUse: UUID? = nil

            // Сначала пытаемся использовать текущий выбранный проект
            if let currentProject = self?.currentProjectId {
                projectToUse = currentProject
                print("🎯 AppDelegate: Используем текущий проект")
            }
            // Если нет текущего, получаем тот же проект, что показывает окно
            else if let lastProjectInfo = self?.breakEndWindow?.getLastUsedProject?(),
                    let foundProject = self?.projectManager.getAllProjects().first(where: { $0.name == lastProjectInfo.name }) {
                projectToUse = foundProject.id
                print("🎯 AppDelegate: Получили проект из окна: \(lastProjectInfo.name) (\(lastProjectInfo.emoji))")
            }
            // В крайнем случае используем lastUsedProjectId
            else {
                projectToUse = self?.lastUsedProjectId
                print("🎯 AppDelegate: Используем lastUsedProjectId")
            }

            self?.currentProjectId = projectToUse

            if let projectId = projectToUse {
                self?.projectManager.markProjectAsUsed(projectId)
                print("🌿 AppDelegate: Запускаем интервал с проектом: \(self?.projectManager.getProject(by: projectId)?.name ?? "Unknown") (ID: \(projectId.uuidString))")
            } else {
                print("🌿 AppDelegate: Запускаем интервал без проекта")
            }

            print("🎯 AppDelegate: currentProjectId перед запуском = \(self?.currentProjectId?.uuidString ?? "nil")")

            // Запускаем интервал напрямую, НЕ через startInterval() который сбрасывает currentProjectId
            self?.pomodoroTimer.startInterval()
            self?.breakEndWindow = nil
        }

        breakEndWindow?.onStartIntervalWithProject = { [weak self] in
            print("🌿 AppDelegate: Выбрать проект для нового интервала")
            self?.showProjectSelection()
            // НЕ обнуляем breakEndWindow здесь - он нужен для обновления после выбора проекта
        }

        breakEndWindow?.onPostpone = { [weak self] in
            print("🌿 AppDelegate: Отложить начало интервала - закрываем окно")

            // Просто закрываем окно, зеленый счетчик уже работает
            self?.breakEndWindow = nil

            // Запускаем таймер на 10 минут (600 секунд) для показа окна снова
            self?.postponeTimer?.invalidate()
            self?.postponeTimer = Timer.scheduledTimer(withTimeInterval: 600, repeats: false) { _ in
                DispatchQueue.main.async {
                    print("🌿 AppDelegate: 10 минут прошло - показываем окно снова")
                    self?.showBreakEndWindow()
                }
            }

            print("🌿 AppDelegate: Окно закрыто, зеленый счетчик продолжает работать")
        }

        breakEndWindow?.showWithAnimation()
    }

    private func showBreakActivityWarning() {
        print("🌿 AppDelegate: Обнаружена активность во время отдыха")

        // Если пользователь сказал, что отдыхает за компом - не показываем уведомления
        if userRestingAtComputer {
            print("🌿 AppDelegate: Пользователь отдыхает за компом - пропускаем уведомление")
            return
        }

        // Если пользователь обещал отдыхать - показываем более агрессивное сообщение
        if userPromisedToRest {
            print("🌿 AppDelegate: Пользователь обещал отдыхать, но активен - показываем строгое предупреждение")
            showStrictBreakWarning()
        } else {
            print("🌿 AppDelegate: Показываем обычное предупреждение об активности")
            showRegularBreakWarning()
        }
    }

    private func showRegularBreakWarning() {
        // Обычное предупреждение (как было раньше)
        if breakStartWindow == nil {
            showBreakStartWindow()
        } else {
            breakStartWindow?.showActivityWarning()
        }
    }

    private func showStrictBreakWarning() {
        // Более агрессивное предупреждение для тех, кто обещал отдыхать
        if breakStartWindow == nil {
            showBreakStartWindow()
            // TODO: Изменить текст на более строгий
        } else {
            breakStartWindow?.showActivityWarning()
            // TODO: Показать более строгое сообщение
        }
    }

    private func showProjectSelection() {
        print("🌿 AppDelegate: Показ быстрого выбора проекта")

        quickProjectSelectionWindow = QuickProjectSelectionWindow(projectManager: projectManager)

        // Позиционируем окно поверх окна завершения перерыва
        if let breakEndWindow = breakEndWindow {
            quickProjectSelectionWindow?.positionRelativeToBreakEndWindow(breakEndWindow)
        }

        // Настраиваем колбэки
        quickProjectSelectionWindow?.onProjectSelected = { [weak self] projectId in
            print("🎯 AppDelegate: Проект выбран: \(projectId.uuidString)")
            if let project = self?.projectManager.getProject(by: projectId) {
                print("🎯 AppDelegate: Название проекта: \(project.name) (\(project.effectiveEmoji))")
            }

            self?.currentProjectId = projectId
            self?.lastUsedProjectId = projectId  // Обновляем и lastUsedProjectId
            self?.projectManager.markProjectAsUsed(projectId)
            self?.quickProjectSelectionWindow = nil

            print("🎯 AppDelegate: currentProjectId установлен = \(projectId.uuidString)")

            // Обновляем отображение проекта в окне завершения перерыва
            if let breakEndWindow = self?.breakEndWindow {
                DispatchQueue.main.async {
                    breakEndWindow.updateProjectButton()
                }
            }

            // НЕ запускаем интервал автоматически - пользователь сам нажмет "Start New Session"
        }

        quickProjectSelectionWindow?.onCancel = { [weak self] in
            self?.quickProjectSelectionWindow = nil
            // Окно завершения перерыва остается открытым
        }

        quickProjectSelectionWindow?.showWithAnimation()
    }

    // Запуск зеленого счетчика отложенного отдыха
    private func startPostponedBreakCounter(with breakStats: BreakStatistics?) {
        print("🌿 AppDelegate: Запускаем зеленый счетчик отложенного отдыха")

        // Сохраняем длительность оригинального отдыха из переданной статистики
        if let stats = breakStats {
            originalBreakDuration = stats.duration

            // Если отдых был очень коротким (< 60 сек), считаем это тестовым режимом
            if originalBreakDuration < 60 {
                originalBreakDuration = 17 * 60 // 17 минут = 1020 секунд
                print("🌿 AppDelegate: Обнаружен тестовый отдых (\(stats.duration)s), имитируем 17-минутный отдых")
            }

            print("🌿 AppDelegate: Оригинальный отдых длился \(originalBreakDuration) секунд (\(Int(originalBreakDuration)/60):\(String(format: "%02d", Int(originalBreakDuration)%60)))")
        } else {
            // Если статистика недоступна, тоже используем 17 минут для демонстрации
            originalBreakDuration = 17 * 60 // 17 минут = 1020 секунд
            print("🌿 AppDelegate: Статистика отдыха недоступна, используем 17-минутный отдых для демонстрации")
        }

        // Отменяем предыдущие таймеры, если они были
        postponeUpdateTimer?.invalidate()

        // Включаем режим отложенного отдыха
        isBreakPostponed = true
        postponedBreakStartTime = Date()

        // СРАЗУ обновляем статус-бар для бесшовного перехода
        updateStatusItem()

        // Запускаем таймер обновления зеленого счетчика каждую секунду
        postponeUpdateTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            DispatchQueue.main.async {
                self?.updateStatusItem()
            }
        }

        print("🌿 AppDelegate: Зеленый счетчик запущен с базовым временем \(originalBreakDuration) секунд")
    }

    // Остановка отложенного отдыха
    private func stopPostponedBreak() {
        print("🌿 AppDelegate: Останавливаем отложенный отдых")
        isBreakPostponed = false
        postponedBreakStartTime = nil
        originalBreakDuration = 0

        postponeTimer?.invalidate()
        postponeTimer = nil

        postponeUpdateTimer?.invalidate()
        postponeUpdateTimer = nil

        updateStatusItem()
    }

    // Генерация текста для отложенного отдыха (зеленый счетчик)
    private func getPostponedBreakStatusText(projectName: String?) -> String {
        guard let startTime = postponedBreakStartTime else {
            return "🌿 Later"
        }

        // Вычисляем, сколько времени прошло с начала отложенного отдыха
        let elapsed = Date().timeIntervalSince(startTime)

        // Добавляем к оригинальной длительности отдыха
        let totalBreakTime = originalBreakDuration + elapsed

        // Отладочные логи
        if elapsed < 5 { // Логи только первые 5 секунд
            print("🌿 DEBUG: originalBreakDuration=\(originalBreakDuration)s, elapsed=\(elapsed)s, total=\(totalBreakTime)s")
        }

        // Форматируем время как MM:SS
        let minutes = Int(totalBreakTime) / 60
        let seconds = Int(totalBreakTime) % 60
        let timeString = String(format: "%d:%02d", minutes, seconds)

        // Добавляем проект если есть
        if let projectName = projectName {
            return "\(projectName) 🌿 \(timeString)"
        } else {
            return "🌿 \(timeString)"
        }
    }

    private func setupBreakWindowUpdates() {
        // Создаем таймер для обновления окна отдыха каждую секунду
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            guard let self = self,
                  let window = self.breakStartWindow,
                  self.pomodoroTimer.state == .onBreak else {
                timer.invalidate()
                return
            }

            // Обновляем время в окне
            window.updateTimeRemaining(self.pomodoroTimer.breakTimeRemaining)

            // Обновляем статус бар
            self.updateStatusItem()
        }
    }

    // Очистка ресурсов при завершении приложения
    func applicationWillTerminate(_ aNotification: Notification) {
        postponeTimer?.invalidate()
        postponeTimer = nil

        postponeUpdateTimer?.invalidate()
        postponeUpdateTimer = nil

        // Останавливаем трекер времени за компьютером
        computerTimeTracker?.stopTracking()
    }
}
